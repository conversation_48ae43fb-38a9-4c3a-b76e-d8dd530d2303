@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    overflow: hidden;
}

.compassui {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: none;
}

.compass-container {
    padding: 8px 20px;
    min-width: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.street-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 400;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
}

.street-name {
    color: #ffffff;
    font-weight: 500;
}

.separator {
    color: #ffffff;
    font-weight: 400;
}

.postal-code {
    color: #ffffff;
    font-weight: 400;
}

.compass-direction {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
    min-width: 20px;
    text-align: center;
}
