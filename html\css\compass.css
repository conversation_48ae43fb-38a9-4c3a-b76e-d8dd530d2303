@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    overflow: hidden;
}

.compassui {
    position: fixed;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: none;
}

.compass-container {
    padding: 15px 25px;
    min-width: 500px;
}

.street-header {
    text-align: center;
    margin-bottom: 15px;
    padding: 8px 0;
}

.street-name {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
}

.postal-code {
    color: #cccccc;
    font-size: 14px;
    font-weight: 500;
    margin-left: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
}

.compass-bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
}

.compass-ticks {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: flex-start;
    gap: 28px;
    width: 400px;
    justify-content: space-between;
}

.tick {
    background: white;
    opacity: 0.9;
}

.tick.major {
    width: 1px;
    height: 18px;
}

.tick.minor {
    width: 1px;
    height: 12px;
}

.compassdir {
    position: relative;
    z-index: 2;
}

.compass-text {
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    font-size: 12px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
}

.compass-pointer {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    color: #ffffff;
    font-size: 22px;
    font-weight: 700;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9);
    z-index: 3;
}
