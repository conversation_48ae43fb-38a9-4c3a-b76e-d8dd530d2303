$((function(){$.post("https://base_compass/uiReady")}));

// Function to convert heading to compass direction
function getCompassDirection(heading) {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const index = Math.round(heading / 45) % 8;
    return directions[index];
}

window.addEventListener("message", (s => {
    const e = s.data;
    switch (e.action) {
        case "opencompass":
            $(".compassui").fadeIn();
            break;
        case "closecompass":
            $(".compassui").fadeOut();
            break;
        case "updatecompass":
            const direction = getCompassDirection(e.heading);
            $(".compass-direction").html(direction);
            break;
        case "updatestreet":
            $(".street-name").html(e.street);
            $(".postal-code").html(e.zone);
            break;
    }
}));
